/**
 * Document Preview Component
 * Shows document content with anonymization highlighting and editing capabilities
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Eye,
  EyeOff,
  Edit3,
  Save,
  RotateCcw,
  Copy,
  Download,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Type,
  Loader2
} from 'lucide-react';

import {
  OCRResult,
  AnonymizationResult,
  HighlightedRange,
  GDPR_ENTITY_TYPES
} from '../../types/gdpr-types';

interface DocumentPreviewProps {
  file: File;
  ocrResult: OCRResult | null;
  anonymizationResult: AnonymizationResult | null;
  editedText: string;
  onTextChange: (text: string) => void;
  showAnonymizations: boolean;
  isProcessing: boolean;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  file,
  ocrResult,
  anonymizationResult,
  editedText,
  onTextChange,
  showAnonymizations,
  isProcessing
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  // ============================================================================
  // Entity Type Colors for Highlighting
  // ============================================================================

  const getEntityColor = (entityType: string): string => {
    const colors: Record<string, string> = {
      PERSON: 'bg-red-200 dark:bg-red-800/30 border-red-300 dark:border-red-700',
      EMAIL_ADDRESS: 'bg-blue-200 dark:bg-blue-800/30 border-blue-300 dark:border-blue-700',
      PHONE_NUMBER: 'bg-green-200 dark:bg-green-800/30 border-green-300 dark:border-green-700',
      LOCATION: 'bg-purple-200 dark:bg-purple-800/30 border-purple-300 dark:border-purple-700',
      ORGANIZATION: 'bg-orange-200 dark:bg-orange-800/30 border-orange-300 dark:border-orange-700',
      DATE_TIME: 'bg-yellow-200 dark:bg-yellow-800/30 border-yellow-300 dark:border-yellow-700',
      CREDIT_CARD: 'bg-pink-200 dark:bg-pink-800/30 border-pink-300 dark:border-pink-700',
      IP_ADDRESS: 'bg-indigo-200 dark:bg-indigo-800/30 border-indigo-300 dark:border-indigo-700',
      URL: 'bg-teal-200 dark:bg-teal-800/30 border-teal-300 dark:border-teal-700',
    };
    return colors[entityType] || 'bg-gray-200 dark:bg-gray-800/30 border-gray-300 dark:border-gray-700';
  };

  // ============================================================================
  // Text Processing and Highlighting
  // ============================================================================

  const renderHighlightedText = (text: string, ranges: HighlightedRange[]): JSX.Element => {
    if (!ranges || ranges.length === 0) {
      return <span>{text}</span>;
    }

    // Sort ranges by start position
    const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);
    const elements: JSX.Element[] = [];
    let lastIndex = 0;

    sortedRanges.forEach((range, index) => {
      // Add text before the highlighted range
      if (range.start > lastIndex) {
        elements.push(
          <span key={`text-${index}`}>
            {text.substring(lastIndex, range.start)}
          </span>
        );
      }

      // Add highlighted text
      elements.push(
        <span
          key={`highlight-${index}`}
          className={`
            inline-block px-1 py-0.5 rounded border-2 border-dashed
            ${getEntityColor(range.entityType)}
            relative group cursor-help
          `}
          title={`${range.entityType} (${Math.round(range.confidence * 100)}% confidence)`}
        >
          {showAnonymizations ? range.replacement : range.originalText}
          
          {/* Tooltip */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-10 whitespace-nowrap">
            {range.entityType} ({Math.round(range.confidence * 100)}%)
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
          </div>
        </span>
      );

      lastIndex = range.end;
    });

    // Add remaining text
    if (lastIndex < text.length) {
      elements.push(
        <span key="text-end">
          {text.substring(lastIndex)}
        </span>
      );
    }

    return <>{elements}</>;
  };

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    if (!isEditing) {
      // Focus textarea when entering edit mode
      setTimeout(() => textareaRef.current?.focus(), 100);
    }
  };

  const handleSave = () => {
    setIsEditing(false);
    // onTextChange is already called by the textarea onChange
  };

  const handleReset = () => {
    if (ocrResult) {
      onTextChange(ocrResult.formattedText);
    }
    setIsEditing(false);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(editedText);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([editedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${file.name.split('.')[0]}_processed.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const adjustFontSize = (delta: number) => {
    setFontSize(prev => Math.max(10, Math.min(24, prev + delta)));
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderToolbar = () => (
    <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-2">
        <h3 className="font-medium text-gray-900 dark:text-white">
          Document Preview
        </h3>
        {ocrResult && (
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {ocrResult.textBlocks.length} text blocks • {ocrResult.metadata.language}
          </span>
        )}
      </div>

      <div className="flex items-center gap-1">
        {/* Font size controls */}
        <button
          onClick={() => adjustFontSize(-1)}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Decrease font size"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        <span className="text-xs text-gray-500 dark:text-gray-400 px-2">
          {fontSize}px
        </span>
        <button
          onClick={() => adjustFontSize(1)}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Increase font size"
        >
          <ZoomIn className="w-4 h-4" />
        </button>

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-600 mx-2" />

        {/* Line numbers toggle */}
        <button
          onClick={() => setShowLineNumbers(!showLineNumbers)}
          className={`p-1.5 rounded transition-colors ${
            showLineNumbers 
              ? 'bg-sakura-100 dark:bg-sakura-900/30 text-sakura-600 dark:text-sakura-400' 
              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          title="Toggle line numbers"
        >
          <Type className="w-4 h-4" />
        </button>

        {/* Edit toggle */}
        <button
          onClick={handleEditToggle}
          className={`p-1.5 rounded transition-colors ${
            isEditing 
              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          title={isEditing ? 'Exit edit mode' : 'Edit text'}
        >
          <Edit3 className="w-4 h-4" />
        </button>

        {isEditing && (
          <>
            <button
              onClick={handleSave}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors text-green-600 dark:text-green-400"
              title="Save changes"
            >
              <Save className="w-4 h-4" />
            </button>
            <button
              onClick={handleReset}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors text-orange-600 dark:text-orange-400"
              title="Reset to original"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </>
        )}

        <div className="w-px h-4 bg-gray-300 dark:bg-gray-600 mx-2" />

        {/* Copy and download */}
        <button
          onClick={handleCopy}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Copy text"
        >
          <Copy className="w-4 h-4" />
        </button>
        <button
          onClick={handleDownload}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Download text"
        >
          <Download className="w-4 h-4" />
        </button>

        {/* Fullscreen toggle */}
        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Toggle fullscreen"
        >
          <Maximize2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );

  const renderContent = () => {
    if (isProcessing) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="w-8 h-8 text-sakura-500 animate-spin mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-400">Processing document...</p>
          </div>
        </div>
      );
    }

    if (!ocrResult) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <Eye className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Upload a document to see preview</p>
          </div>
        </div>
      );
    }

    const lines = editedText.split('\n');

    if (isEditing) {
      return (
        <textarea
          ref={textareaRef}
          value={editedText}
          onChange={(e) => onTextChange(e.target.value)}
          className="w-full h-full min-h-[400px] p-4 bg-transparent border-none outline-none resize-none font-mono"
          style={{ fontSize: `${fontSize}px` }}
          placeholder="Edit your document text here..."
        />
      );
    }

    return (
      <div 
        ref={previewRef}
        className="p-4 h-full min-h-[400px] overflow-auto font-mono leading-relaxed"
        style={{ fontSize: `${fontSize}px` }}
      >
        {showLineNumbers ? (
          <div className="flex">
            <div className="flex-shrink-0 pr-4 text-gray-400 dark:text-gray-600 select-none border-r border-gray-200 dark:border-gray-700 mr-4">
              {lines.map((_, index) => (
                <div key={index} className="text-right">
                  {index + 1}
                </div>
              ))}
            </div>
            <div className="flex-1">
              {anonymizationResult && showAnonymizations ? (
                renderHighlightedText(editedText, anonymizationResult.highlightedRanges)
              ) : (
                <pre className="whitespace-pre-wrap">{editedText}</pre>
              )}
            </div>
          </div>
        ) : (
          <div>
            {anonymizationResult && showAnonymizations ? (
              renderHighlightedText(editedText, anonymizationResult.highlightedRanges)
            ) : (
              <pre className="whitespace-pre-wrap">{editedText}</pre>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderLegend = () => {
    if (!anonymizationResult || !showAnonymizations) return null;

    const entityTypes = [...new Set(anonymizationResult.highlightedRanges.map(r => r.entityType))];

    return (
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          Anonymized Entities
        </h4>
        <div className="flex flex-wrap gap-2">
          {entityTypes.map(entityType => (
            <div
              key={entityType}
              className={`
                px-2 py-1 rounded text-xs border-2 border-dashed
                ${getEntityColor(entityType)}
              `}
            >
              {entityType.replace('_', ' ')}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className={`
      bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700
      ${isFullscreen ? 'fixed inset-4 z-50' : ''}
    `}>
      {renderToolbar()}
      {renderContent()}
      {renderLegend()}
    </div>
  );
};

export default DocumentPreview;
