{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/scheme/scheme.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scheme/scheme.ts\nvar conf = {\n  comments: {\n    lineComment: \";\",\n    blockComment: [\"#|\", \"|#\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".scheme\",\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  keywords: [\n    \"case\",\n    \"do\",\n    \"let\",\n    \"loop\",\n    \"if\",\n    \"else\",\n    \"when\",\n    \"cons\",\n    \"car\",\n    \"cdr\",\n    \"cond\",\n    \"lambda\",\n    \"lambda*\",\n    \"syntax-rules\",\n    \"format\",\n    \"set!\",\n    \"quote\",\n    \"eval\",\n    \"append\",\n    \"list\",\n    \"list?\",\n    \"member?\",\n    \"load\"\n  ],\n  constants: [\"#t\", \"#f\"],\n  operators: [\"eq?\", \"eqv?\", \"equal?\", \"and\", \"or\", \"not\", \"null?\"],\n  tokenizer: {\n    root: [\n      [/#[xXoObB][0-9a-fA-F]+/, \"number.hex\"],\n      [/[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?/, \"number.float\"],\n      [\n        /(?:\\b(?:(define|define-syntax|define-macro))\\b)(\\s+)((?:\\w|\\-|\\!|\\?)*)/,\n        [\"keyword\", \"white\", \"variable\"]\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /[a-zA-Z_#][a-zA-Z0-9_\\-\\?\\!\\*]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@operators\": \"operators\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [/[^\\|#]+/, \"comment\"],\n      [/#\\|/, \"comment\", \"@push\"],\n      [/\\|#/, \"comment\", \"@pop\"],\n      [/[\\|#]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#\\|/, \"comment\", \"@comment\"],\n      [/;.*$/, \"comment\"]\n    ],\n    strings: [\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"(?=.)/, \"string\", \"@multiLineString\"]\n    ],\n    multiLineString: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,MAAM,IAAI;AAAA,EACtB,WAAW,CAAC,OAAO,QAAQ,UAAU,OAAO,MAAM,OAAO,OAAO;AAAA,EAChE,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,yBAAyB,YAAY;AAAA,MACtC,CAAC,4CAA4C,cAAc;AAAA,MAC3D;AAAA,QACE;AAAA,QACA,CAAC,WAAW,SAAS,UAAU;AAAA,MACjC;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,OAAO,WAAW,OAAO;AAAA,MAC1B,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,OAAO,WAAW,UAAU;AAAA,MAC7B,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,UAAU,UAAU,kBAAkB;AAAA,IACzC;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,EACF;AACF;", "names": []}