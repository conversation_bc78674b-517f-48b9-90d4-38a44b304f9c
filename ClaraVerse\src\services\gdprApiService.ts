/**
 * GDPR API Service
 * Handles all API communications for GDPR document processing
 */

import {
  OCRRequest,
  OCRResult,
  AnonymizationRequest,
  AnonymizationResult,
  AnonymizationPreview,
  DeanonymizationRequest,
  DocumentProcessRequest,
  DocumentProcessResponse,
  APIResponse,
  Collection
} from '../types/gdpr-types';

class GDPRApiService {
  private baseUrl: string;

  constructor() {
    // Use the same backend URL as Clara
    this.baseUrl = 'http://localhost:5000';
  }

  /**
   * Convert file to base64 string
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix (e.g., "data:application/pdf;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Handle API responses with error checking
   */
  private async handleResponse<T>(response: Response): Promise<APIResponse<T>> {
    try {
      const data = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: data.detail || data.error || `HTTP ${response.status}`,
          data: undefined
        };
      }

      return {
        success: true,
        data: data,
        error: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // OCR API Methods
  // ============================================================================

  /**
   * Process document with OCR
   */
  async processOCR(file: File, options: Partial<OCRRequest> = {}): Promise<APIResponse<OCRResult>> {
    try {
      const fileBase64 = await this.fileToBase64(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';

      const request: OCRRequest = {
        fileBase64,
        fileType,
        preserveLayout: options.preserveLayout ?? true,
        language: options.language ?? 'eng+fra',
        dpi: options.dpi ?? 300
      };

      const response = await fetch(`${this.baseUrl}/ocr/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<OCRResult>(response);
    } catch (error) {
      return {
        success: false,
        error: `OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // GDPR Anonymization API Methods
  // ============================================================================

  /**
   * Anonymize text for GDPR compliance
   */
  async anonymizeText(request: AnonymizationRequest): Promise<APIResponse<AnonymizationResult>> {
    try {
      const response = await fetch(`${this.baseUrl}/gdpr/anonymize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<AnonymizationResult>(response);
    } catch (error) {
      return {
        success: false,
        error: `Anonymization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Preview anonymization without actually anonymizing
   */
  async previewAnonymization(text: string, language: string = 'en'): Promise<APIResponse<AnonymizationPreview>> {
    try {
      const response = await fetch(`${this.baseUrl}/gdpr/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text, language })
      });

      return this.handleResponse<{ preview: AnonymizationPreview }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Preview failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Restore original text from anonymized version
   */
  async deanonymizeText(request: DeanonymizationRequest): Promise<APIResponse<{ restoredText: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/gdpr/deanonymize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<{ restoredText: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Deanonymization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // Combined Document Processing API Methods
  // ============================================================================

  /**
   * Process document with OCR and optional GDPR anonymization
   */
  async processDocumentWithGDPR(
    file: File, 
    options: Partial<DocumentProcessRequest> = {}
  ): Promise<APIResponse<DocumentProcessResponse>> {
    try {
      const fileBase64 = await this.fileToBase64(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';

      const request: DocumentProcessRequest = {
        fileBase64,
        fileType,
        filename: file.name,
        collectionName: options.collectionName ?? 'default_collection',
        anonymize: options.anonymize ?? true,
        preserveLayout: options.preserveLayout ?? true,
        ocrLanguage: options.ocrLanguage ?? 'eng+fra',
        anonymizationLanguage: options.anonymizationLanguage ?? 'en'
      };

      const response = await fetch(`${this.baseUrl}/documents/process-with-gdpr`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<DocumentProcessResponse>(response);
    } catch (error) {
      return {
        success: false,
        error: `Document processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // Collection Management API Methods
  // ============================================================================

  /**
   * Get all collections
   */
  async getCollections(): Promise<APIResponse<{ collections: Collection[] }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections`);
      return this.handleResponse<{ collections: Collection[] }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to fetch collections: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Create a new collection
   */
  async createCollection(name: string, description?: string): Promise<APIResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, description })
      });

      return this.handleResponse<{ message: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to create collection: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Delete a collection
   */
  async deleteCollection(collectionName: string): Promise<APIResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections/${collectionName}`, {
        method: 'DELETE'
      });

      return this.handleResponse<{ message: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete collection: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Check if GDPR/OCR services are available
   */
  async checkServiceAvailability(): Promise<APIResponse<{ available: boolean; services: string[] }>> {
    try {
      // Test both OCR and GDPR endpoints with minimal requests
      const testRequests = await Promise.allSettled([
        fetch(`${this.baseUrl}/gdpr/preview`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: 'test', language: 'en' })
        }),
        fetch(`${this.baseUrl}/ocr/process`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            fileBase64: 'dGVzdA==', // base64 for "test"
            fileType: 'txt' 
          })
        })
      ]);

      const availableServices: string[] = [];
      
      if (testRequests[0].status === 'fulfilled' && testRequests[0].value.status !== 503) {
        availableServices.push('GDPR');
      }
      
      if (testRequests[1].status === 'fulfilled' && testRequests[1].value.status !== 503) {
        availableServices.push('OCR');
      }

      return {
        success: true,
        data: {
          available: availableServices.length > 0,
          services: availableServices
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Service check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { available: false, services: [] }
      };
    }
  }

  /**
   * Get supported file formats
   */
  getSupportedFormats(): string[] {
    return [
      'pdf',   // Scanned PDFs
      'png', 'jpg', 'jpeg', 'tiff', 'bmp', 'gif',  // Images
      'docx',  // Word documents
      'txt', 'md'  // Text files
    ];
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedFormats();
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      return {
        valid: false,
        error: `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`
      };
    }

    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  }
}

// Export singleton instance
export const gdprApiService = new GDPRApiService();
export default gdprApiService;
