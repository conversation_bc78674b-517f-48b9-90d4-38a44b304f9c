/**
 * Processing Workflow Component
 * Shows the step-by-step progress of document processing
 */

import React from 'react';
import {
  Upload,
  FileText,
  Shield,
  Database,
  CheckCircle,
  AlertCircle,
  Loader2,
  Clock,
  ArrowRight
} from 'lucide-react';

import {
  DocumentProcessingWorkflow,
  DocumentProcessingStep,
  StepStatus
} from '../../types/gdpr-types';

interface ProcessingWorkflowProps {
  workflow: DocumentProcessingWorkflow;
  onStepClick?: (stepId: string) => void;
}

const ProcessingWorkflow: React.FC<ProcessingWorkflowProps> = ({
  workflow,
  onStepClick
}) => {
  // ============================================================================
  // Step Configuration
  // ============================================================================

  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case 'upload':
        return Upload;
      case 'ocr':
        return FileText;
      case 'anonymize':
        return Shield;
      case 'rag':
        return Database;
      default:
        return FileText;
    }
  };

  const getStepColor = (status: StepStatus) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800';
      case 'running':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800';
      case 'error':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800';
      case 'pending':
        return 'text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700';
      case 'skipped':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800';
      default:
        return 'text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700';
    }
  };

  const getStatusIcon = (status: StepStatus) => {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'running':
        return Loader2;
      case 'error':
        return AlertCircle;
      case 'pending':
        return Clock;
      case 'skipped':
        return Clock;
      default:
        return Clock;
    }
  };

  // ============================================================================
  // Utility Functions
  // ============================================================================

  const formatDuration = (startTime?: Date, endTime?: Date): string => {
    if (!startTime) return '';
    
    const end = endTime || new Date();
    const duration = end.getTime() - startTime.getTime();
    
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  const getProgressPercentage = (step: DocumentProcessingStep): number => {
    if (step.status === 'completed') return 100;
    if (step.status === 'error' || step.status === 'skipped') return 0;
    return step.progress || 0;
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderStepIcon = (step: DocumentProcessingStep) => {
    const StepIcon = getStepIcon(step.id);
    const StatusIcon = getStatusIcon(step.status);
    const colorClasses = getStepColor(step.status);

    return (
      <div className={`
        relative w-12 h-12 rounded-full border-2 flex items-center justify-center
        ${colorClasses}
      `}>
        <StepIcon className="w-6 h-6" />
        
        {/* Status indicator */}
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center">
          <StatusIcon className={`
            w-3 h-3
            ${step.status === 'running' ? 'animate-spin' : ''}
            ${step.status === 'completed' ? 'text-green-500' : ''}
            ${step.status === 'error' ? 'text-red-500' : ''}
            ${step.status === 'pending' ? 'text-gray-400' : ''}
          `} />
        </div>
      </div>
    );
  };

  const renderProgressBar = (step: DocumentProcessingStep) => {
    const percentage = getProgressPercentage(step);
    
    return (
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
        <div
          className={`
            h-2 rounded-full transition-all duration-300
            ${step.status === 'completed' ? 'bg-green-500' : ''}
            ${step.status === 'running' ? 'bg-blue-500' : ''}
            ${step.status === 'error' ? 'bg-red-500' : ''}
            ${step.status === 'pending' ? 'bg-gray-300 dark:bg-gray-600' : ''}
          `}
          style={{ width: `${percentage}%` }}
        />
      </div>
    );
  };

  const renderStepDetails = (step: DocumentProcessingStep) => (
    <div className="flex-1 ml-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-gray-900 dark:text-white">
          {step.name}
        </h3>
        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          {step.startTime && (
            <span>{formatDuration(step.startTime, step.endTime)}</span>
          )}
          {step.status === 'running' && step.progress !== undefined && (
            <span>{Math.round(step.progress)}%</span>
          )}
        </div>
      </div>

      {step.message && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {step.message}
        </p>
      )}

      {step.error && (
        <p className="text-sm text-red-600 dark:text-red-400 mt-1">
          Error: {step.error}
        </p>
      )}

      {(step.status === 'running' || step.status === 'completed') && (
        renderProgressBar(step)
      )}
    </div>
  );

  const renderConnector = (index: number, isLast: boolean) => {
    if (isLast) return null;

    const currentStep = workflow.steps[index];
    const nextStep = workflow.steps[index + 1];
    
    const isActive = currentStep.status === 'completed' || nextStep.status !== 'pending';

    return (
      <div className="flex items-center justify-center py-2">
        <ArrowRight className={`
          w-5 h-5 transition-colors
          ${isActive 
            ? 'text-sakura-500 dark:text-sakura-400' 
            : 'text-gray-300 dark:text-gray-600'
          }
        `} />
      </div>
    );
  };

  const renderOverallProgress = () => {
    const completedSteps = workflow.steps.filter(step => step.status === 'completed').length;
    const totalSteps = workflow.steps.length;
    const overallPercentage = (completedSteps / totalSteps) * 100;

    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Processing Progress
          </h2>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {completedSteps} of {totalSteps} steps completed
          </span>
        </div>
        
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            className="h-3 bg-gradient-to-r from-sakura-500 to-sakura-600 rounded-full transition-all duration-500"
            style={{ width: `${overallPercentage}%` }}
          />
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>0%</span>
          <span>{Math.round(overallPercentage)}%</span>
          <span>100%</span>
        </div>
      </div>
    );
  };

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      {renderOverallProgress()}

      <div className="space-y-4">
        {workflow.steps.map((step, index) => (
          <div key={step.id}>
            <div
              className={`
                flex items-start p-4 rounded-lg border transition-all duration-200
                ${step.status === 'running' 
                  ? 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800' 
                  : 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700'
                }
                ${onStepClick ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700' : ''}
              `}
              onClick={() => onStepClick?.(step.id)}
            >
              {renderStepIcon(step)}
              {renderStepDetails(step)}
            </div>
            
            {renderConnector(index, index === workflow.steps.length - 1)}
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">
            Document ID: {workflow.documentId}
          </span>
          <span className={`
            px-2 py-1 rounded-full text-xs font-medium
            ${workflow.status === 'completed' 
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' 
              : workflow.status === 'error'
              ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'
              : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
            }
          `}>
            {workflow.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProcessingWorkflow;
