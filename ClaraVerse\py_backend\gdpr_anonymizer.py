"""
GDPR Anonymization Module using Microsoft Presidio
Provides anonymization and de-anonymization capabilities for text documents
"""

import logging
import json
import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from presidio_analyzer import AnalyzerEngine
from presidio_anonymizer import AnonymizerEngine
from presidio_anonymizer.entities import RecognizerResult, OperatorConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AnonymizationMapping:
    """Stores the mapping between original and anonymized text"""
    original_text: str
    anonymized_text: str
    entity_type: str
    start_pos: int
    end_pos: int
    replacement_id: str

@dataclass
class AnonymizationResult:
    """Result of anonymization process"""
    anonymized_text: str
    mappings: List[AnonymizationMapping]
    highlighted_ranges: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class GDPRAnonymizer:
    """
    GDPR-compliant anonymization service using Microsoft Presidio
    """
    
    def __init__(self):
        """Initialize the anonymizer with Presidio engines"""
        try:
            self.analyzer = AnalyzerEngine()
            self.anonymizer = AnonymizerEngine()
            self.replacement_counter = 0
            logger.info("GDPR Anonymizer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize GDPR Anonymizer: {e}")
            raise
    
    def _generate_replacement_id(self, entity_type: str) -> str:
        """Generate a unique replacement ID"""
        self.replacement_counter += 1
        return f"[{entity_type}_{self.replacement_counter:04d}]"
    
    def _get_anonymization_operators(self) -> Dict[str, OperatorConfig]:
        """Define anonymization operators for different entity types"""
        return {
            "PERSON": OperatorConfig("replace", {"new_value": "[PERSON_XXXX]"}),
            "EMAIL_ADDRESS": OperatorConfig("replace", {"new_value": "[EMAIL_XXXX]"}),
            "PHONE_NUMBER": OperatorConfig("replace", {"new_value": "[PHONE_XXXX]"}),
            "CREDIT_CARD": OperatorConfig("replace", {"new_value": "[CARD_XXXX]"}),
            "IBAN_CODE": OperatorConfig("replace", {"new_value": "[IBAN_XXXX]"}),
            "IP_ADDRESS": OperatorConfig("replace", {"new_value": "[IP_XXXX]"}),
            "LOCATION": OperatorConfig("replace", {"new_value": "[LOCATION_XXXX]"}),
            "ORGANIZATION": OperatorConfig("replace", {"new_value": "[ORG_XXXX]"}),
            "DATE_TIME": OperatorConfig("replace", {"new_value": "[DATE_XXXX]"}),
            "NRP": OperatorConfig("replace", {"new_value": "[ID_XXXX]"}),  # National Registration Number
            "MEDICAL_LICENSE": OperatorConfig("replace", {"new_value": "[MED_XXXX]"}),
            "URL": OperatorConfig("replace", {"new_value": "[URL_XXXX]"}),
        }
    
    def analyze_text(self, text: str, language: str = "en") -> List[RecognizerResult]:
        """Analyze text to identify PII entities"""
        try:
            results = self.analyzer.analyze(
                text=text,
                language=language,
                entities=None  # Analyze all supported entities
            )
            logger.info(f"Found {len(results)} PII entities in text")
            return results
        except Exception as e:
            logger.error(f"Error analyzing text: {e}")
            return []
    
    def anonymize_text(self, text: str, language: str = "en", 
                      custom_operators: Optional[Dict[str, OperatorConfig]] = None) -> AnonymizationResult:
        """
        Anonymize text while preserving structure and creating mappings
        """
        try:
            # Analyze text for PII entities
            analyzer_results = self.analyze_text(text, language)
            
            if not analyzer_results:
                return AnonymizationResult(
                    anonymized_text=text,
                    mappings=[],
                    highlighted_ranges=[],
                    metadata={"entities_found": 0, "language": language}
                )
            
            # Use custom operators or default ones
            operators = custom_operators or self._get_anonymization_operators()
            
            # Create mappings before anonymization
            mappings = []
            highlighted_ranges = []
            
            # Sort results by start position (descending) to avoid position shifts
            sorted_results = sorted(analyzer_results, key=lambda x: x.start, reverse=True)
            
            anonymized_text = text
            
            for result in sorted_results:
                original_value = text[result.start:result.end]
                replacement_id = self._generate_replacement_id(result.entity_type)
                
                # Create mapping
                mapping = AnonymizationMapping(
                    original_text=original_value,
                    anonymized_text=replacement_id,
                    entity_type=result.entity_type,
                    start_pos=result.start,
                    end_pos=result.end,
                    replacement_id=replacement_id
                )
                mappings.append(mapping)
                
                # Create highlighted range for UI
                highlighted_ranges.append({
                    "start": result.start,
                    "end": result.end,
                    "entity_type": result.entity_type,
                    "confidence": result.score,
                    "original_text": original_value,
                    "replacement": replacement_id
                })
                
                # Replace in text
                anonymized_text = (
                    anonymized_text[:result.start] + 
                    replacement_id + 
                    anonymized_text[result.end:]
                )
            
            # Update positions in highlighted_ranges after all replacements
            # (positions are already correct since we processed in reverse order)
            
            return AnonymizationResult(
                anonymized_text=anonymized_text,
                mappings=mappings,
                highlighted_ranges=highlighted_ranges,
                metadata={
                    "entities_found": len(analyzer_results),
                    "language": language,
                    "entity_types": list(set(r.entity_type for r in analyzer_results))
                }
            )
            
        except Exception as e:
            logger.error(f"Error anonymizing text: {e}")
            raise
    
    def deanonymize_text(self, anonymized_text: str, mappings: List[AnonymizationMapping]) -> str:
        """
        Restore original text using the mappings
        """
        try:
            restored_text = anonymized_text
            
            # Sort mappings by replacement_id to ensure consistent replacement
            sorted_mappings = sorted(mappings, key=lambda x: x.replacement_id)
            
            for mapping in sorted_mappings:
                restored_text = restored_text.replace(
                    mapping.replacement_id, 
                    mapping.original_text
                )
            
            return restored_text
            
        except Exception as e:
            logger.error(f"Error deanonymizing text: {e}")
            raise
    
    def partial_deanonymize(self, anonymized_text: str, mappings: List[AnonymizationMapping], 
                           restore_entities: List[str]) -> str:
        """
        Partially restore text by only restoring specific entity types
        """
        try:
            restored_text = anonymized_text
            
            for mapping in mappings:
                if mapping.entity_type in restore_entities:
                    restored_text = restored_text.replace(
                        mapping.replacement_id,
                        mapping.original_text
                    )
            
            return restored_text
            
        except Exception as e:
            logger.error(f"Error in partial deanonymization: {e}")
            raise
    
    def get_anonymization_preview(self, text: str, language: str = "en") -> Dict[str, Any]:
        """
        Get a preview of what would be anonymized without actually anonymizing
        """
        try:
            analyzer_results = self.analyze_text(text, language)
            
            preview_data = {
                "total_entities": len(analyzer_results),
                "entity_types": {},
                "entities": []
            }
            
            for result in analyzer_results:
                entity_info = {
                    "text": text[result.start:result.end],
                    "type": result.entity_type,
                    "start": result.start,
                    "end": result.end,
                    "confidence": result.score
                }
                preview_data["entities"].append(entity_info)
                
                # Count entity types
                if result.entity_type not in preview_data["entity_types"]:
                    preview_data["entity_types"][result.entity_type] = 0
                preview_data["entity_types"][result.entity_type] += 1
            
            return preview_data
            
        except Exception as e:
            logger.error(f"Error generating anonymization preview: {e}")
            raise
    
    def export_mappings(self, mappings: List[AnonymizationMapping]) -> str:
        """Export mappings to JSON string"""
        try:
            mappings_dict = [asdict(mapping) for mapping in mappings]
            return json.dumps(mappings_dict, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error exporting mappings: {e}")
            raise
    
    def import_mappings(self, mappings_json: str) -> List[AnonymizationMapping]:
        """Import mappings from JSON string"""
        try:
            mappings_dict = json.loads(mappings_json)
            return [AnonymizationMapping(**mapping) for mapping in mappings_dict]
        except Exception as e:
            logger.error(f"Error importing mappings: {e}")
            raise

# Example usage and testing
if __name__ == "__main__":
    # Test the anonymizer
    anonymizer = GDPRAnonymizer()
    
    test_text = """
    Bonjour, je suis Jean Dupont, mon <NAME_EMAIL> 
    et mon téléphone est 06 12 34 56 78. J'habite à Paris et je travaille 
    chez Microsoft France. Ma carte de crédit est 4532 1234 5678 9012.
    """
    
    print("Original text:")
    print(test_text)
    
    # Get preview
    preview = anonymizer.get_anonymization_preview(test_text, "fr")
    print(f"\nPreview: {preview}")
    
    # Anonymize
    result = anonymizer.anonymize_text(test_text, "fr")
    print(f"\nAnonymized text:")
    print(result.anonymized_text)
    
    print(f"\nMappings found: {len(result.mappings)}")
    for mapping in result.mappings:
        print(f"- {mapping.entity_type}: '{mapping.original_text}' -> '{mapping.anonymized_text}'")
    
    # Restore
    restored = anonymizer.deanonymize_text(result.anonymized_text, result.mappings)
    print(f"\nRestored text:")
    print(restored)
