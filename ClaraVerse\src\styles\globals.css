/* Resizable Widget Styles */
.resizable-widget {
  position: relative;
  transition: all 0.2s ease;
  width: 100%;
  height: 100%;
}

/* Global scrollbar hiding - Apply to all scrollable elements */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Scrollbar hiding utilities */
.scrollbar-none {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scrollbar-none::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Hide scrollbars globally for specific containers */
.overflow-y-auto,
.overflow-auto,
.overflow-x-auto,
.overflow-y-scroll,
.overflow-x-scroll {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.overflow-y-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar,
.overflow-y-scroll::-webkit-scrollbar,
.overflow-x-scroll::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbars for common HTML elements */
html, body, div, section, article, main, aside, nav, header, footer,
ul, ol, li, table, tbody, thead, tr, td, th,
textarea, pre, code, iframe {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar, body::-webkit-scrollbar, div::-webkit-scrollbar,
section::-webkit-scrollbar, article::-webkit-scrollbar, main::-webkit-scrollbar,
aside::-webkit-scrollbar, nav::-webkit-scrollbar, header::-webkit-scrollbar,
footer::-webkit-scrollbar, ul::-webkit-scrollbar, ol::-webkit-scrollbar,
li::-webkit-scrollbar, table::-webkit-scrollbar, tbody::-webkit-scrollbar,
thead::-webkit-scrollbar, tr::-webkit-scrollbar, td::-webkit-scrollbar,
th::-webkit-scrollbar, textarea::-webkit-scrollbar, pre::-webkit-scrollbar,
code::-webkit-scrollbar, iframe::-webkit-scrollbar {
  display: none;
}

.resizable-widget:hover .resize-handle {
  opacity: 1;
}

.resize-handle {
  position: absolute;
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: rgba(219, 39, 119, 0.3); /* sakura color */
  border: 2px solid rgba(219, 39, 119, 0.5);
  z-index: 50;
}

.resize-handle:hover {
  background-color: rgba(219, 39, 119, 0.5);
  transform: scale(1.1);
  transition: all 0.2s ease;
}

.widget-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  position: relative;
}

/* Dark mode adjustments */
.dark .resize-handle {
  background-color: rgba(244, 114, 182, 0.3); /* lighter sakura color for dark mode */
  border: 2px solid rgba(244, 114, 182, 0.5);
}

.dark .resize-handle:hover {
  background-color: rgba(244, 114, 182, 0.5);
}

/* Specific handle styles */
.resize-handle-n {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 8px;
  cursor: ns-resize;
  border-radius: 4px;
}

.resize-handle-s {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 8px;
  cursor: ns-resize;
  border-radius: 4px;
}

.resize-handle-e {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 8px;
  height: 40px;
  cursor: ew-resize;
  border-radius: 4px;
}

.resize-handle-w {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 8px;
  height: 40px;
  cursor: ew-resize;
  border-radius: 4px;
}

.resize-handle-ne {
  top: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: ne-resize;
  border-radius: 50%;
}

.resize-handle-nw {
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  border-radius: 50%;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: se-resize;
  border-radius: 50%;
}

.resize-handle-sw {
  bottom: 0;
  left: 0;
  width: 16px;
  height: 16px;
  cursor: sw-resize;
  border-radius: 50%;
} 