/**
 * GDPR Document Processor
 * Main component for document processing with OCR and GDPR anonymization
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Shield, 
  FileText, 
  Eye, 
  EyeOff, 
  Settings, 
  Upload,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowLeft
} from 'lucide-react';

import {
  DocumentStatus,
  DocumentProcessingWorkflow,
  GDPRProcessingSettings,
  ProcessingError,
  Collection,
  OCRResult,
  AnonymizationResult,
  DocumentProcessResponse
} from '../types/gdpr-types';

import gdprApiService from '../services/gdprApiService';

// Import sub-components (we'll create these next)
import DocumentUpload from './GDPRDocumentProcessor/DocumentUpload';
import DocumentPreview from './GDPRDocumentProcessor/DocumentPreview';
import AnonymizationControls from './GDPRDocumentProcessor/AnonymizationControls';
import ProcessingWorkflow from './GDPRDocumentProcessor/ProcessingWorkflow';

interface GDPRDocumentProcessorProps {
  onPageChange: (page: string) => void;
}

const GDPRDocumentProcessor: React.FC<GDPRDocumentProcessorProps> = ({ onPageChange }) => {
  // ============================================================================
  // State Management
  // ============================================================================

  const [currentStep, setCurrentStep] = useState<'upload' | 'preview' | 'anonymize' | 'complete'>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [ocrResult, setOcrResult] = useState<OCRResult | null>(null);
  const [anonymizationResult, setAnonymizationResult] = useState<AnonymizationResult | null>(null);
  const [editedText, setEditedText] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingError, setProcessingError] = useState<ProcessingError | null>(null);
  const [workflow, setWorkflow] = useState<DocumentProcessingWorkflow | null>(null);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string>('default_collection');
  const [serviceAvailable, setServiceAvailable] = useState<boolean>(false);
  const [showAnonymizations, setShowAnonymizations] = useState<boolean>(true);

  // Settings state
  const [settings, setSettings] = useState<GDPRProcessingSettings>({
    defaultOcrLanguage: 'eng+fra',
    defaultAnonymizationLanguage: 'en',
    preserveLayoutByDefault: true,
    autoAnonymizeByDefault: true,
    defaultCollection: 'default_collection',
    enabledEntityTypes: ['PERSON', 'EMAIL_ADDRESS', 'PHONE_NUMBER', 'LOCATION'],
    ocrDpi: 300
  });

  // ============================================================================
  // Initialization and Service Check
  // ============================================================================

  useEffect(() => {
    initializeComponent();
  }, []);

  const initializeComponent = async () => {
    try {
      // Check service availability
      const serviceCheck = await gdprApiService.checkServiceAvailability();
      setServiceAvailable(serviceCheck.data?.available || false);

      if (!serviceCheck.data?.available) {
        setProcessingError({
          code: 'SERVICE_UNAVAILABLE',
          message: 'GDPR/OCR services are not available. Please check backend configuration.',
          timestamp: new Date()
        });
        return;
      }

      // Load collections
      const collectionsResponse = await gdprApiService.getCollections();
      if (collectionsResponse.success && collectionsResponse.data) {
        setCollections(collectionsResponse.data.collections);
      }

    } catch (error) {
      setProcessingError({
        code: 'INITIALIZATION_ERROR',
        message: `Failed to initialize: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      });
    }
  };

  // ============================================================================
  // File Upload Handlers
  // ============================================================================

  const handleFileSelected = useCallback(async (file: File) => {
    setSelectedFile(file);
    setProcessingError(null);
    setOcrResult(null);
    setAnonymizationResult(null);
    setEditedText('');

    // Validate file
    const validation = gdprApiService.validateFile(file);
    if (!validation.valid) {
      setProcessingError({
        code: 'INVALID_FILE',
        message: validation.error || 'Invalid file',
        timestamp: new Date()
      });
      return;
    }

    // Start OCR processing
    await processOCR(file);
  }, []);

  // ============================================================================
  // OCR Processing
  // ============================================================================

  const processOCR = async (file: File) => {
    setIsProcessing(true);
    setCurrentStep('preview');

    try {
      const response = await gdprApiService.processOCR(file, {
        preserveLayout: settings.preserveLayoutByDefault,
        language: settings.defaultOcrLanguage,
        dpi: settings.ocrDpi
      });

      if (response.success && response.data) {
        setOcrResult(response.data);
        setEditedText(response.data.formattedText);

        // Auto-proceed to anonymization if enabled
        if (settings.autoAnonymizeByDefault) {
          await processAnonymization(response.data.formattedText);
        }
      } else {
        throw new Error(response.error || 'OCR processing failed');
      }

    } catch (error) {
      setProcessingError({
        code: 'OCR_ERROR',
        message: `OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        step: 'ocr'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // ============================================================================
  // Anonymization Processing
  // ============================================================================

  const processAnonymization = async (text: string) => {
    setIsProcessing(true);
    setCurrentStep('anonymize');

    try {
      const response = await gdprApiService.anonymizeText({
        text,
        language: settings.defaultAnonymizationLanguage,
        preserveLayout: settings.preserveLayoutByDefault
      });

      if (response.success && response.data) {
        setAnonymizationResult(response.data);
        setEditedText(response.data.anonymizedText);
      } else {
        throw new Error(response.error || 'Anonymization failed');
      }

    } catch (error) {
      setProcessingError({
        code: 'ANONYMIZATION_ERROR',
        message: `Anonymization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        step: 'anonymization'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // ============================================================================
  // Final Processing and RAG Integration
  // ============================================================================

  const finalizeDocument = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setCurrentStep('complete');

    try {
      const response = await gdprApiService.processDocumentWithGDPR(selectedFile, {
        collectionName: selectedCollection,
        anonymize: !!anonymizationResult,
        preserveLayout: settings.preserveLayoutByDefault,
        ocrLanguage: settings.defaultOcrLanguage,
        anonymizationLanguage: settings.defaultAnonymizationLanguage
      });

      if (response.success && response.data) {
        // Document successfully processed and added to RAG
        console.log('Document processed successfully:', response.data);
        
        // Reset for next document
        setTimeout(() => {
          resetProcessor();
        }, 2000);
      } else {
        throw new Error(response.error || 'Final processing failed');
      }

    } catch (error) {
      setProcessingError({
        code: 'FINALIZATION_ERROR',
        message: `Final processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        step: 'finalization'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // ============================================================================
  // Utility Functions
  // ============================================================================

  const resetProcessor = () => {
    setCurrentStep('upload');
    setSelectedFile(null);
    setOcrResult(null);
    setAnonymizationResult(null);
    setEditedText('');
    setProcessingError(null);
    setWorkflow(null);
  };

  const toggleAnonymizationView = () => {
    setShowAnonymizations(!showAnonymizations);
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <button
          onClick={() => onPageChange('dashboard')}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <Shield className="w-8 h-8 text-sakura-500" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            GDPR Document Processor
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Process documents with OCR and GDPR-compliant anonymization
          </p>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {anonymizationResult && (
          <button
            onClick={toggleAnonymizationView}
            className="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            {showAnonymizations ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showAnonymizations ? 'Hide' : 'Show'} Anonymizations
          </button>
        )}
        
        <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
          <Settings className="w-5 h-5" />
        </button>
      </div>
    </div>
  );

  const renderServiceUnavailable = () => (
    <div className="flex flex-col items-center justify-center h-64 text-center">
      <AlertCircle className="w-16 h-16 text-red-500 mb-4" />
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        Service Unavailable
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        GDPR/OCR services are not available. Please check the backend configuration.
      </p>
      <button
        onClick={initializeComponent}
        className="px-4 py-2 bg-sakura-500 text-white rounded-lg hover:bg-sakura-600 transition-colors"
      >
        Retry
      </button>
    </div>
  );

  const renderError = () => (
    processingError && (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-2 mb-2">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <h3 className="font-semibold text-red-800 dark:text-red-200">
            Processing Error
          </h3>
        </div>
        <p className="text-red-700 dark:text-red-300 mb-2">
          {processingError.message}
        </p>
        {processingError.step && (
          <p className="text-sm text-red-600 dark:text-red-400">
            Error occurred during: {processingError.step}
          </p>
        )}
        <button
          onClick={() => setProcessingError(null)}
          className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
        >
          Dismiss
        </button>
      </div>
    )
  );

  // ============================================================================
  // Main Render
  // ============================================================================

  if (!serviceAvailable) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-sakura-100 dark:from-gray-900 dark:to-sakura-100 p-6">
        {renderHeader()}
        {renderServiceUnavailable()}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-sakura-100 dark:from-gray-900 dark:to-sakura-100 p-6">
      {renderHeader()}
      {renderError()}

      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Upload and Controls */}
          <div className="lg:col-span-1 space-y-6">
            <DocumentUpload
              onFileSelected={handleFileSelected}
              selectedFile={selectedFile}
              isProcessing={isProcessing}
              supportedFormats={gdprApiService.getSupportedFormats()}
            />

            {ocrResult && (
              <AnonymizationControls
                ocrResult={ocrResult}
                anonymizationResult={anonymizationResult}
                onAnonymize={processAnonymization}
                onFinalize={finalizeDocument}
                isProcessing={isProcessing}
                collections={collections}
                selectedCollection={selectedCollection}
                onCollectionChange={setSelectedCollection}
                settings={settings}
                onSettingsChange={setSettings}
              />
            )}
          </div>

          {/* Right Column - Preview and Workflow */}
          <div className="lg:col-span-2 space-y-6">
            {selectedFile && (
              <DocumentPreview
                file={selectedFile}
                ocrResult={ocrResult}
                anonymizationResult={anonymizationResult}
                editedText={editedText}
                onTextChange={setEditedText}
                showAnonymizations={showAnonymizations}
                isProcessing={isProcessing}
              />
            )}

            {workflow && (
              <ProcessingWorkflow
                workflow={workflow}
                onStepClick={(stepId) => console.log('Step clicked:', stepId)}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GDPRDocumentProcessor;
