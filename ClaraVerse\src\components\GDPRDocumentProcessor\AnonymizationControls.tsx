/**
 * Anonymization Controls Component
 * Controls for GDPR anonymization settings and actions
 */

import React, { useState } from 'react';
import {
  Shield,
  Eye,
  Settings,
  Play,
  CheckCircle,
  AlertTriangle,
  Database,
  Globe,
  Loader2,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import {
  OCRResult,
  AnonymizationResult,
  Collection,
  GDPRProcessingSettings,
  GDPR_ENTITY_TYPES
} from '../../types/gdpr-types';

interface AnonymizationControlsProps {
  ocrResult: OCRResult;
  anonymizationResult: AnonymizationResult | null;
  onAnonymize: (text: string) => Promise<void>;
  onFinalize: () => Promise<void>;
  isProcessing: boolean;
  collections: Collection[];
  selectedCollection: string;
  onCollectionChange: (collection: string) => void;
  settings: GDPRProcessingSettings;
  onSettingsChange: (settings: GDPRProcessingSettings) => void;
}

const AnonymizationControls: React.FC<AnonymizationControlsProps> = ({
  ocrResult,
  anonymizationResult,
  onAnonymize,
  onFinalize,
  isProcessing,
  collections,
  selectedCollection,
  onCollectionChange,
  settings,
  onSettingsChange
}) => {
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [showEntitySettings, setShowEntitySettings] = useState(false);

  // ============================================================================
  // Entity Type Configuration
  // ============================================================================

  const entityTypeConfigs = [
    { type: 'PERSON', label: 'Names', description: 'Personal names', color: 'red' },
    { type: 'EMAIL_ADDRESS', label: 'Emails', description: 'Email addresses', color: 'blue' },
    { type: 'PHONE_NUMBER', label: 'Phone Numbers', description: 'Phone numbers', color: 'green' },
    { type: 'LOCATION', label: 'Locations', description: 'Addresses and locations', color: 'purple' },
    { type: 'ORGANIZATION', label: 'Organizations', description: 'Company names', color: 'orange' },
    { type: 'DATE_TIME', label: 'Dates', description: 'Dates and times', color: 'yellow' },
    { type: 'CREDIT_CARD', label: 'Credit Cards', description: 'Credit card numbers', color: 'pink' },
    { type: 'IP_ADDRESS', label: 'IP Addresses', description: 'IP addresses', color: 'indigo' },
    { type: 'URL', label: 'URLs', description: 'Web addresses', color: 'teal' },
  ];

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleAnonymize = async () => {
    if (ocrResult) {
      await onAnonymize(ocrResult.formattedText);
    }
  };

  const handleEntityTypeToggle = (entityType: string) => {
    const newEnabledTypes = settings.enabledEntityTypes.includes(entityType as any)
      ? settings.enabledEntityTypes.filter(type => type !== entityType)
      : [...settings.enabledEntityTypes, entityType as any];

    onSettingsChange({
      ...settings,
      enabledEntityTypes: newEnabledTypes
    });
  };

  const handleLanguageChange = (field: 'defaultOcrLanguage' | 'defaultAnonymizationLanguage', value: string) => {
    onSettingsChange({
      ...settings,
      [field]: value
    });
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderAnonymizationStatus = () => {
    if (!anonymizationResult) {
      return (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                Ready for Anonymization
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Click "Anonymize Document" to detect and anonymize sensitive data
              </p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
          <h3 className="font-medium text-green-800 dark:text-green-200">
            Anonymization Complete
          </h3>
        </div>
        <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
          <p>Found {anonymizationResult.metadata.entitiesFound} sensitive entities</p>
          <p>Entity types: {anonymizationResult.metadata.entityTypes.join(', ')}</p>
        </div>
      </div>
    );
  };

  const renderCollectionSelector = () => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Target Collection
      </label>
      <select
        value={selectedCollection}
        onChange={(e) => onCollectionChange(e.target.value)}
        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-sakura-500 focus:border-transparent"
      >
        {collections.map(collection => (
          <option key={collection.name} value={collection.name}>
            {collection.name} ({collection.documentCount} docs)
          </option>
        ))}
      </select>
      <p className="text-xs text-gray-500 dark:text-gray-400">
        The processed document will be added to this collection for RAG
      </p>
    </div>
  );

  const renderLanguageSettings = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          OCR Language
        </label>
        <select
          value={settings.defaultOcrLanguage}
          onChange={(e) => handleLanguageChange('defaultOcrLanguage', e.target.value)}
          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-sakura-500 focus:border-transparent"
        >
          <option value="eng">English</option>
          <option value="fra">French</option>
          <option value="eng+fra">English + French</option>
          <option value="deu">German</option>
          <option value="spa">Spanish</option>
          <option value="ita">Italian</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Anonymization Language
        </label>
        <select
          value={settings.defaultAnonymizationLanguage}
          onChange={(e) => handleLanguageChange('defaultAnonymizationLanguage', e.target.value)}
          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-sakura-500 focus:border-transparent"
        >
          <option value="en">English</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="es">Spanish</option>
          <option value="it">Italian</option>
        </select>
      </div>
    </div>
  );

  const renderEntityTypeSettings = () => (
    <div className="space-y-3">
      <button
        onClick={() => setShowEntitySettings(!showEntitySettings)}
        className="flex items-center justify-between w-full text-left"
      >
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Entity Types to Anonymize
        </span>
        {showEntitySettings ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </button>

      {showEntitySettings && (
        <div className="space-y-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
          {entityTypeConfigs.map(config => (
            <label key={config.type} className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enabledEntityTypes.includes(config.type as any)}
                onChange={() => handleEntityTypeToggle(config.type)}
                className="rounded border-gray-300 text-sakura-600 focus:ring-sakura-500"
              />
              <div className="flex-1">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {config.label}
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {config.description}
                </p>
              </div>
            </label>
          ))}
        </div>
      )}
    </div>
  );

  const renderAdvancedSettings = () => (
    <div className="space-y-4">
      <button
        onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
        className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
      >
        <Settings className="w-4 h-4" />
        Advanced Settings
        {showAdvancedSettings ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </button>

      {showAdvancedSettings && (
        <div className="space-y-4 pl-6 border-l-2 border-gray-200 dark:border-gray-700">
          {renderLanguageSettings()}
          {renderEntityTypeSettings()}
          
          <div className="space-y-2">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={settings.preserveLayoutByDefault}
                onChange={(e) => onSettingsChange({
                  ...settings,
                  preserveLayoutByDefault: e.target.checked
                })}
                className="rounded border-gray-300 text-sakura-600 focus:ring-sakura-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Preserve document layout
              </span>
            </label>

            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoAnonymizeByDefault}
                onChange={(e) => onSettingsChange({
                  ...settings,
                  autoAnonymizeByDefault: e.target.checked
                })}
                className="rounded border-gray-300 text-sakura-600 focus:ring-sakura-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Auto-anonymize after OCR
              </span>
            </label>
          </div>
        </div>
      )}
    </div>
  );

  const renderActionButtons = () => (
    <div className="space-y-3">
      {!anonymizationResult && (
        <button
          onClick={handleAnonymize}
          disabled={isProcessing}
          className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-sakura-500 text-white rounded-lg hover:bg-sakura-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isProcessing ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Shield className="w-5 h-5" />
          )}
          {isProcessing ? 'Anonymizing...' : 'Anonymize Document'}
        </button>
      )}

      {anonymizationResult && (
        <button
          onClick={onFinalize}
          disabled={isProcessing}
          className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isProcessing ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Database className="w-5 h-5" />
          )}
          {isProcessing ? 'Adding to RAG...' : 'Add to RAG Collection'}
        </button>
      )}
    </div>
  );

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className="space-y-6">
      {/* Anonymization Status */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          GDPR Anonymization
        </h2>
        {renderAnonymizationStatus()}
      </div>

      {/* Collection Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          RAG Integration
        </h3>
        {renderCollectionSelector()}
      </div>

      {/* Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Processing Settings
        </h3>
        {renderAdvancedSettings()}
      </div>

      {/* Action Buttons */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        {renderActionButtons()}
      </div>
    </div>
  );
};

export default AnonymizationControls;
