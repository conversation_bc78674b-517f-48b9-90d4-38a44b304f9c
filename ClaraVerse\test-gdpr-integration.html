<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GDPR Document Processor - ClaraVerse</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .highlight-person { background-color: rgba(239, 68, 68, 0.3); border: 2px dashed rgb(239, 68, 68); }
        .highlight-email { background-color: rgba(59, 130, 246, 0.3); border: 2px dashed rgb(59, 130, 246); }
        .highlight-phone { background-color: rgba(34, 197, 94, 0.3); border: 2px dashed rgb(34, 197, 94); }
        .highlight-location { background-color: rgba(168, 85, 247, 0.3); border: 2px dashed rgb(168, 85, 247); }
    </style>
</head>
<body class="bg-gradient-to-br from-white to-pink-100 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-8">
            <div class="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">GDPR Document Processor</h1>
                <p class="text-gray-600">Test d'intégration - ClaraVerse</p>
            </div>
        </div>

        <!-- Status -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center gap-2">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="font-semibold text-green-800">Intégration Complète Réussie !</h3>
            </div>
            <p class="text-green-700 mt-2">
                Tous les composants GDPR ont été créés et intégrés avec succès dans ClaraVerse.
            </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Backend -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">Backend Python</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>✅ Module d'anonymisation GDPR</li>
                    <li>✅ Processeur OCR avec Tesseract</li>
                    <li>✅ API endpoints FastAPI</li>
                    <li>✅ Intégration RAG</li>
                </ul>
            </div>

            <!-- Frontend -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">Frontend React</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>✅ Composant principal GDPR</li>
                    <li>✅ Upload de documents</li>
                    <li>✅ Aperçu avec surlignage</li>
                    <li>✅ Contrôles d'anonymisation</li>
                </ul>
            </div>

            <!-- Integration -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">Intégration UI</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>✅ Menu principal mis à jour</li>
                    <li>✅ Routing dans App.tsx</li>
                    <li>✅ Types TypeScript</li>
                    <li>✅ Service API complet</li>
                </ul>
            </div>
        </div>

        <!-- Demo Preview -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Aperçu de l'Anonymisation GDPR</h3>
            <div class="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                <p class="mb-2">Texte original :</p>
                <div class="bg-white p-3 rounded border">
                    Bonjour, je suis <span class="highlight-person px-1 rounded">Jean Dupont</span>, 
                    mon email est <span class="highlight-email px-1 rounded"><EMAIL></span> 
                    et mon téléphone est <span class="highlight-phone px-1 rounded">06 12 34 56 78</span>. 
                    J'habite à <span class="highlight-location px-1 rounded">Paris</span>.
                </div>
                
                <p class="mt-4 mb-2">Texte anonymisé :</p>
                <div class="bg-white p-3 rounded border">
                    Bonjour, je suis <span class="highlight-person px-1 rounded">[PERSON_0001]</span>, 
                    mon email est <span class="highlight-email px-1 rounded">[EMAIL_0001]</span> 
                    et mon téléphone est <span class="highlight-phone px-1 rounded">[PHONE_0001]</span>. 
                    J'habite à <span class="highlight-location px-1 rounded">[LOCATION_0001]</span>.
                </div>
            </div>
        </div>

        <!-- Workflow -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Workflow de Traitement</h3>
            <div class="flex items-center justify-between">
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">1</div>
                    <p class="text-sm text-gray-600 mt-2">Upload</p>
                </div>
                <div class="flex-1 h-0.5 bg-gray-300 mx-4"></div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">2</div>
                    <p class="text-sm text-gray-600 mt-2">OCR</p>
                </div>
                <div class="flex-1 h-0.5 bg-gray-300 mx-4"></div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">3</div>
                    <p class="text-sm text-gray-600 mt-2">GDPR</p>
                </div>
                <div class="flex-1 h-0.5 bg-gray-300 mx-4"></div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center text-white font-semibold">4</div>
                    <p class="text-sm text-gray-600 mt-2">RAG</p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="font-semibold text-blue-800 mb-3">🚀 Comment tester :</h3>
            <ol class="text-blue-700 space-y-2">
                <li><strong>1.</strong> Démarrer le backend Python : <code class="bg-blue-100 px-2 py-1 rounded">cd py_backend && python main.py</code></li>
                <li><strong>2.</strong> Démarrer le frontend : <code class="bg-blue-100 px-2 py-1 rounded">npm run dev</code></li>
                <li><strong>3.</strong> Ouvrir ClaraVerse dans le navigateur</li>
                <li><strong>4.</strong> Cliquer sur <strong>"GDPR Processor"</strong> dans le menu latéral</li>
                <li><strong>5.</strong> Glisser-déposer un document PDF, image ou DOCX</li>
                <li><strong>6.</strong> Voir l'OCR et l'anonymisation en action !</li>
            </ol>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-500">
            <p>GDPR Document Processor intégré avec succès dans ClaraVerse 🎉</p>
            <p class="text-sm mt-1">Système complet : OCR + Anonymisation RGPD + Intégration RAG</p>
        </div>
    </div>
</body>
</html>
