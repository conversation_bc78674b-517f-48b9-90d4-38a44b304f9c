"""
OCR Processing Module with Layout Preservation
Handles OCR for scanned PDFs, images, and document processing
"""

import logging
import io
import os
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from PIL import Image
import pytesseract
from pdf2image import convert_from_bytes, convert_from_path
import docx
from docx.document import Document as DocxDocument

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TextBlock:
    """Represents a block of text with position information"""
    text: str
    x: int
    y: int
    width: int
    height: int
    confidence: float
    page_number: int = 1

@dataclass
class OCRResult:
    """Result of OCR processing"""
    text: str
    formatted_text: str  # Text with preserved formatting
    text_blocks: List[TextBlock]
    metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None

class OCRProcessor:
    """
    OCR processor with layout preservation capabilities
    """
    
    def __init__(self, tesseract_cmd: Optional[str] = None):
        """
        Initialize OCR processor
        
        Args:
            tesseract_cmd: Path to tesseract executable (optional)
        """
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
        
        # Test if tesseract is available
        try:
            pytesseract.get_tesseract_version()
            logger.info("Tesseract OCR initialized successfully")
        except Exception as e:
            logger.warning(f"Tesseract not found or not working: {e}")
    
    def process_image(self, image: Union[Image.Image, bytes, str], 
                     preserve_layout: bool = True, 
                     language: str = 'eng+fra') -> OCRResult:
        """
        Process a single image with OCR
        
        Args:
            image: PIL Image, image bytes, or path to image file
            preserve_layout: Whether to preserve text layout
            language: OCR language (e.g., 'eng', 'fra', 'eng+fra')
        """
        try:
            # Convert input to PIL Image
            if isinstance(image, str):
                pil_image = Image.open(image)
            elif isinstance(image, bytes):
                pil_image = Image.open(io.BytesIO(image))
            else:
                pil_image = image
            
            # Ensure image is in RGB mode
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            text_blocks = []
            
            if preserve_layout:
                # Get detailed OCR data with bounding boxes
                ocr_data = pytesseract.image_to_data(
                    pil_image, 
                    lang=language,
                    output_type=pytesseract.Output.DICT
                )
                
                # Process OCR data to create text blocks
                for i in range(len(ocr_data['text'])):
                    text = ocr_data['text'][i].strip()
                    if text and int(ocr_data['conf'][i]) > 30:  # Filter low confidence
                        block = TextBlock(
                            text=text,
                            x=int(ocr_data['left'][i]),
                            y=int(ocr_data['top'][i]),
                            width=int(ocr_data['width'][i]),
                            height=int(ocr_data['height'][i]),
                            confidence=float(ocr_data['conf'][i]) / 100.0,
                            page_number=1
                        )
                        text_blocks.append(block)
                
                # Create formatted text preserving layout
                formatted_text = self._reconstruct_layout(text_blocks, pil_image.size)
                
            else:
                # Simple text extraction
                formatted_text = pytesseract.image_to_string(pil_image, lang=language)
            
            # Get plain text
            plain_text = pytesseract.image_to_string(pil_image, lang=language)
            
            return OCRResult(
                text=plain_text,
                formatted_text=formatted_text,
                text_blocks=text_blocks,
                metadata={
                    "image_size": pil_image.size,
                    "language": language,
                    "preserve_layout": preserve_layout,
                    "total_blocks": len(text_blocks)
                },
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error processing image with OCR: {e}")
            return OCRResult(
                text="",
                formatted_text="",
                text_blocks=[],
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    def process_pdf(self, pdf_data: bytes, 
                   preserve_layout: bool = True,
                   language: str = 'eng+fra',
                   dpi: int = 300) -> OCRResult:
        """
        Process a PDF file with OCR (for scanned PDFs)
        
        Args:
            pdf_data: PDF file as bytes
            preserve_layout: Whether to preserve text layout
            language: OCR language
            dpi: DPI for PDF to image conversion
        """
        try:
            # Convert PDF to images
            images = convert_from_bytes(pdf_data, dpi=dpi)
            
            all_text_blocks = []
            all_text = []
            all_formatted_text = []
            
            for page_num, image in enumerate(images, 1):
                logger.info(f"Processing PDF page {page_num}/{len(images)}")
                
                # Process each page
                page_result = self.process_image(image, preserve_layout, language)
                
                if page_result.success:
                    # Update page numbers for text blocks
                    for block in page_result.text_blocks:
                        block.page_number = page_num
                    
                    all_text_blocks.extend(page_result.text_blocks)
                    all_text.append(f"--- Page {page_num} ---\n{page_result.text}")
                    all_formatted_text.append(f"--- Page {page_num} ---\n{page_result.formatted_text}")
                else:
                    logger.warning(f"Failed to process page {page_num}: {page_result.error_message}")
            
            combined_text = "\n\n".join(all_text)
            combined_formatted = "\n\n".join(all_formatted_text)
            
            return OCRResult(
                text=combined_text,
                formatted_text=combined_formatted,
                text_blocks=all_text_blocks,
                metadata={
                    "total_pages": len(images),
                    "language": language,
                    "preserve_layout": preserve_layout,
                    "dpi": dpi,
                    "total_blocks": len(all_text_blocks)
                },
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error processing PDF with OCR: {e}")
            return OCRResult(
                text="",
                formatted_text="",
                text_blocks=[],
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    def process_docx(self, docx_data: bytes) -> OCRResult:
        """
        Process a DOCX file (extract text, no OCR needed)
        
        Args:
            docx_data: DOCX file as bytes
        """
        try:
            # Load DOCX document
            doc = docx.Document(io.BytesIO(docx_data))
            
            paragraphs = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)
            
            # Also extract text from tables
            tables_text = []
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        tables_text.append(" | ".join(row_text))
            
            # Combine all text
            all_text = paragraphs + tables_text
            combined_text = "\n".join(all_text)
            
            return OCRResult(
                text=combined_text,
                formatted_text=combined_text,  # DOCX already has structure
                text_blocks=[],  # No position info for DOCX
                metadata={
                    "paragraphs": len(paragraphs),
                    "tables": len(doc.tables),
                    "document_type": "docx"
                },
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error processing DOCX: {e}")
            return OCRResult(
                text="",
                formatted_text="",
                text_blocks=[],
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    def process_text_file(self, text_data: bytes, encoding: str = 'utf-8') -> OCRResult:
        """
        Process a plain text file
        
        Args:
            text_data: Text file as bytes
            encoding: Text encoding
        """
        try:
            text = text_data.decode(encoding)
            
            return OCRResult(
                text=text,
                formatted_text=text,
                text_blocks=[],
                metadata={
                    "encoding": encoding,
                    "document_type": "text",
                    "length": len(text)
                },
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error processing text file: {e}")
            return OCRResult(
                text="",
                formatted_text="",
                text_blocks=[],
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    def _reconstruct_layout(self, text_blocks: List[TextBlock], image_size: Tuple[int, int]) -> str:
        """
        Reconstruct text layout from text blocks
        
        Args:
            text_blocks: List of text blocks with position info
            image_size: (width, height) of the original image
        """
        if not text_blocks:
            return ""
        
        # Sort blocks by vertical position (top to bottom), then horizontal (left to right)
        sorted_blocks = sorted(text_blocks, key=lambda b: (b.y, b.x))
        
        # Group blocks into lines based on Y position
        lines = []
        current_line = []
        current_y = sorted_blocks[0].y
        y_threshold = 20  # Pixels tolerance for same line
        
        for block in sorted_blocks:
            if abs(block.y - current_y) <= y_threshold:
                current_line.append(block)
            else:
                if current_line:
                    lines.append(current_line)
                current_line = [block]
                current_y = block.y
        
        if current_line:
            lines.append(current_line)
        
        # Reconstruct text line by line
        reconstructed_lines = []
        for line in lines:
            # Sort blocks in line by X position (left to right)
            line_blocks = sorted(line, key=lambda b: b.x)
            line_text = " ".join(block.text for block in line_blocks)
            reconstructed_lines.append(line_text)
        
        return "\n".join(reconstructed_lines)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return [
            'pdf',  # Scanned PDFs
            'png', 'jpg', 'jpeg', 'tiff', 'bmp', 'gif',  # Images
            'docx',  # Word documents
            'txt', 'md'  # Text files
        ]

# Example usage and testing
if __name__ == "__main__":
    processor = OCRProcessor()
    
    print("Supported formats:", processor.get_supported_formats())
    
    # Test with a sample image (if available)
    try:
        # Create a simple test image with text
        from PIL import Image, ImageDraw, ImageFont
        
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font, fallback to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 50), "Hello World!", fill='black', font=font)
        draw.text((10, 100), "This is a test document.", fill='black', font=font)
        
        # Process the test image
        result = processor.process_image(img)
        
        print(f"\nOCR Result:")
        print(f"Success: {result.success}")
        print(f"Text: {result.text}")
        print(f"Text blocks: {len(result.text_blocks)}")
        
    except Exception as e:
        print(f"Test failed: {e}")
