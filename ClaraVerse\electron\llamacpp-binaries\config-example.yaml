# Auto-generated llama-swap configuration (EXAMPLE - original file would be config.yaml)
# Models directory: /Users/<USER>/.clara/llama-models

healthCheckTimeout: 30
logLevel: info

models:
  "qwen3:8b-iq4_nl":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/DeepSeek-R1-0528-Qwen3-8B-IQ4_NL.gguf"
      --port 9999 --jinja --n-gpu-layers 40 --threads 5 --ctx-size 8192 --batch-size 512 --ubatch-size 128 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "llama32:1b":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/Llama-3.2-1B-Instruct-Q4_K_M.gguf"
      --port 9999 --jinja --n-gpu-layers 22 --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "qwen25:3b":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/Qwen2.5-VL-3B-Instruct-q4_k_m.gguf"
      --port 9999 --jinja --n-gpu-layers 26
      --mmproj "/Users/<USER>/.clara/llama-models/Qwen2.5-VL-3B-Instruct-mmproj-f16.gguf" --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "qwen3:8b-q4_k_m":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/Qwen3-8B-Q4_K_M.gguf"
      --port 9999 --jinja --n-gpu-layers 40 --threads 5 --ctx-size 8192 --batch-size 512 --ubatch-size 128 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "gemma3:4b":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/gemma-3-4b-it-Q4_K_M.gguf"
      --port 9999 --jinja --n-gpu-layers 32 --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "mxbai-embed-large:large":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/mxbai-embed-large-v1.Q4_K_M.gguf"
      --port 9999 --jinja --n-gpu-layers 22 --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "nomic:latest":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/nomic-embed-text-v1.5.Q2_K.gguf"
      --port 9999 --jinja --n-gpu-layers 22 --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

  "llama11:1.1b":
    proxy: "http://127.0.0.1:9999"
    cmd: |
      "/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64/llama-server"
      -m "/Users/<USER>/.clara/llama-models/tinyllama-1.1b-chat-v1.0.Q4_0.gguf"
      --port 9999 --jinja --n-gpu-layers 22 --threads 5 --ctx-size 8192 --batch-size 256 --ubatch-size 64 --keep 1024 --defrag-thold 0.1 --mlock --parallel 1 --flash-attn --cont-batching
    env:
      - "DYLD_LIBRARY_PATH=/Users/<USER>/Documents/ClaraVerse/electron/llamacpp-binaries/darwin-arm64:"
    ttl: 300

groups:
  "default_group":
    swap: true
    exclusive: true
    members:
      - "qwen3:8b-iq4_nl"
      - "llama32:1b"
      - "qwen25:3b"
      - "qwen3:8b-q4_k_m"
      - "gemma3:4b"
      - "mxbai-embed-large:large"
      - "nomic:latest"
      - "llama11:1.1b"
