# Web framework
fastapi
uvicorn
python-multipart  # For file uploads
pydantic # Using 1.x for better compatibility

# Lang<PERSON>hain and related
langchain
langchain-community
langchain-core
chromadb
langchain-chroma
langchain-ollama

# Document processing
pypdf
python-docx
pandas

# Utilities
requests

# IMPORTANT: Keep numpy locked at a compatible version
numpy

faster-whisper

diffusers[torch]
torch
transformers
safetensors
accelerate

# Text-to-Speech
gtts
pyttsx3

# spaCy for NLP (required by Kokoro/misaki)
spacy>=3.4.0

# Kokoro TTS
kokoro>=0.9.4
kokoro-onnx>=0.4.9
soundfile>=0.12.1
misaki[en]>=0.1.0
onnxruntime>=1.16.0

# GDPR Anonymization
presidio-analyzer>=2.2.0
presidio-anonymizer>=2.2.0

# OCR and Image Processing
pytesseract>=0.3.10
Pillow>=10.0.0
pdf2image>=3.1.0

# Additional NLP models for Presidio
spacy[en_core_web_sm]