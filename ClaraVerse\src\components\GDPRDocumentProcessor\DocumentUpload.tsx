/**
 * Document Upload Component
 * Specialized file upload for GDPR document processing
 */

import React, { useState, useRef, useCallback } from 'react';
import {
  Upload,
  File,
  FileText,
  Image as ImageIcon,
  X,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface DocumentUploadProps {
  onFileSelected: (file: File) => void;
  selectedFile: File | null;
  isProcessing: boolean;
  supportedFormats: string[];
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onFileSelected,
  selectedFile,
  isProcessing,
  supportedFormats
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ============================================================================
  // File Handling
  // ============================================================================

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      return {
        valid: false,
        error: `Unsupported file format. Supported: ${supportedFormats.join(', ')}`
      };
    }

    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  };

  const handleFileSelection = useCallback((file: File) => {
    setUploadError(null);
    
    const validation = validateFile(file);
    if (!validation.valid) {
      setUploadError(validation.error || 'Invalid file');
      return;
    }

    onFileSelected(file);
  }, [onFileSelected, supportedFormats]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]); // Only take the first file
    }
  }, [handleFileSelection]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFileSelection]);

  const triggerFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const clearSelectedFile = useCallback(() => {
    onFileSelected(null as any); // Reset selection
    setUploadError(null);
  }, [onFileSelected]);

  // ============================================================================
  // Utility Functions
  // ============================================================================

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'].includes(extension || '')) {
      return ImageIcon;
    }
    if (extension === 'pdf') {
      return FileText;
    }
    return File;
  };

  const getFileTypeDescription = (file: File): string => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return 'PDF Document';
      case 'docx':
        return 'Word Document';
      case 'txt':
        return 'Text File';
      case 'md':
        return 'Markdown File';
      case 'png':
      case 'jpg':
      case 'jpeg':
        return 'Image File';
      default:
        return 'Document';
    }
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderUploadArea = () => (
    <div
      className={`
        relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200
        ${isDragOver 
          ? 'border-sakura-500 bg-sakura-50 dark:bg-sakura-900/20' 
          : 'border-gray-300 dark:border-gray-700 hover:border-sakura-400 dark:hover:border-sakura-600'
        }
        ${isProcessing ? 'opacity-50 pointer-events-none' : 'cursor-pointer'}
      `}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={triggerFileSelect}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept={supportedFormats.map(format => `.${format}`).join(',')}
        onChange={handleFileInputChange}
        disabled={isProcessing}
      />

      <div className="flex flex-col items-center gap-4">
        <div className={`
          w-16 h-16 rounded-full flex items-center justify-center
          ${isDragOver 
            ? 'bg-sakura-100 dark:bg-sakura-800' 
            : 'bg-gray-100 dark:bg-gray-800'
          }
        `}>
          {isProcessing ? (
            <Loader2 className="w-8 h-8 text-sakura-500 animate-spin" />
          ) : (
            <Upload className={`w-8 h-8 ${isDragOver ? 'text-sakura-600' : 'text-gray-500'}`} />
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {isProcessing ? 'Processing...' : 'Upload Document'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            Drag and drop your document here, or click to browse
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500">
            Supported formats: {supportedFormats.join(', ').toUpperCase()}
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-600 mt-1">
            Maximum file size: 50MB
          </p>
        </div>
      </div>
    </div>
  );

  const renderSelectedFile = () => {
    if (!selectedFile) return null;

    const IconComponent = getFileIcon(selectedFile);

    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-sakura-100 dark:bg-sakura-900/30 rounded-lg flex items-center justify-center">
            <IconComponent className="w-6 h-6 text-sakura-600 dark:text-sakura-400" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 dark:text-white truncate">
              {selectedFile.name}
            </h4>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <span>{getFileTypeDescription(selectedFile)}</span>
              <span>•</span>
              <span>{formatFileSize(selectedFile.size)}</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {isProcessing ? (
              <div className="flex items-center gap-2 text-sm text-sakura-600 dark:text-sakura-400">
                <Loader2 className="w-4 h-4 animate-spin" />
                Processing...
              </div>
            ) : (
              <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                Ready
              </div>
            )}

            {!isProcessing && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearSelectedFile();
                }}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderError = () => {
    if (!uploadError) return null;

    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
        <div className="flex items-center gap-2">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700 dark:text-red-300">
            {uploadError}
          </p>
        </div>
      </div>
    );
  };

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Document Upload
        </h2>

        {selectedFile ? renderSelectedFile() : renderUploadArea()}
        {renderError()}

        {selectedFile && !isProcessing && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={triggerFileSelect}
              className="w-full px-4 py-2 text-sm text-sakura-600 dark:text-sakura-400 hover:bg-sakura-50 dark:hover:bg-sakura-900/20 rounded-lg transition-colors"
            >
              Select Different File
            </button>
          </div>
        )}
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">
                Processing Document
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Extracting text and analyzing content...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentUpload;
